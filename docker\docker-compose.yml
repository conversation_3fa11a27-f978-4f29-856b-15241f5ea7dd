name: fodmap-docker-compose

services:
  db:
    container_name: fodmap-db-container
    image: postgres:15-alpine
    env_file: .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../migrations/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - fodmap-network

  api:
    container_name: fodmap-api-container
    build:
      context: ../app
      dockerfile: Dockerfile
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://${APP_DB_USER}:${APP_DB_PASSWORD}@db:5432/${POSTGRES_DB}
      - NODE_ENV=development
    ports:
      - "3001:3000"
    volumes:
      # Mount source code for development (hot reload)
      - ../app:/app
      - /app/node_modules
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fodmap-network
    restart: unless-stopped
    command: npm run dev

volumes:
  postgres_data:

networks:
  fodmap-network:
    driver: bridge
