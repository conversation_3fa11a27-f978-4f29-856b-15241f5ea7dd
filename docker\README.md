# FODMAP Application Docker Setup

This directory contains Docker configuration for running the FODMAP Recipe API with PostgreSQL database.

## Quick Start

1. **Copy environment file:**
   ```bash
   cd docker
   cp .env.example .env
   ```

2. **Edit the `.env` file with your passwords:**
   ```bash
   # Update these values:
   POSTGRES_PASSWORD=your_strong_postgres_password_here
   APP_DB_PASSWORD=your_app_password_here
   ```

3. **Start the application:**
   ```bash
   docker-compose up -d
   ```

4. **Check if services are running:**
   ```bash
   docker-compose ps
   ```

5. **View logs:**
   ```bash
   # All services
   docker-compose logs -f
   
   # Just the API
   docker-compose logs -f api
   
   # Just the database
   docker-compose logs -f db
   ```

## Services

- **Database (db)**: PostgreSQL 15 with automatic schema initialization
- **API (api)**: Node.js backend application

## Endpoints

- **API**: http://localhost:3000
- **Health Check**: http://localhost:3000/health
- **Database**: localhost:5432

## Useful Commands

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: deletes all data)
docker-compose down -v

# Rebuild and restart
docker-compose up --build -d

# View container status
docker-compose ps

# Execute commands in containers
docker-compose exec api sh
docker-compose exec db psql -U postgres -d fodmap_db

# View real-time logs
docker-compose logs -f api
```

## Development vs Production

This setup is configured for production use. For development:

1. Change `NODE_ENV=development` in `.env`
2. Add volume mount for live code reloading:
   ```yaml
   volumes:
     - ../app:/app
     - /app/node_modules
   ```

## Troubleshooting

1. **Database connection issues**: Ensure the database is healthy before the API starts
2. **Port conflicts**: Change ports in docker-compose.yml if 3000 or 5432 are in use
3. **Permission issues**: The API runs as non-root user for security

## Data Persistence

Database data is stored in Docker volume `postgres_data` and persists between container restarts.
